#!/bin/bash

# MOER_OVERSEAS 服务器部署脚本
# 用法: ./server_deploy.sh [service1] [service2] ...
# 如果没有指定服务，则部署所有服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# 检查 Docker 和 Docker Compose 是否安装
check_dependencies() {
    log "检查依赖..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi

    log_success "依赖检查通过"
}

# 拉取最新镜像
pull_images() {
    log "拉取最新镜像..."
    docker-compose pull || docker compose pull
    log_success "镜像拉取完成"
}

# 部署指定服务
deploy_services() {
    local services=("$@")

    if [ ${#services[@]} -eq 0 ]; then
        log "未指定服务，部署所有服务..."
        docker-compose up -d || docker compose up -d
    else
        log "部署指定服务: ${services[*]}"
        docker-compose up -d "${services[@]}" || docker compose up -d "${services[@]}"
    fi

    log_success "服务部署完成"
}

# 健康检查函数
health_check() {
    local services=("$@")

    log "开始健康检查..."

    # 等待容器启动
    log "等待容器启动（5秒）..."
    sleep 5

    for service in "${services[@]}"; do
        log "检查服务: $service"

        # 获取容器ID
        local container_id
        if command -v docker-compose &> /dev/null; then
            container_id=$(docker-compose ps -q "$service" 2>/dev/null)
        else
            container_id=$(docker compose ps -q "$service" 2>/dev/null)
        fi

        if [ -z "$container_id" ]; then
            log_error "无法找到服务 $service 的容器ID"
            continue
        fi

        # 检查容器状态
        local status=$(docker inspect -f '{{.State.Status}}' "$container_id" 2>/dev/null)
        log "容器 $service 状态: $status"

        if [ "$status" != "running" ]; then
            log_error "容器 $service 未处于运行状态 (当前: $status)"
            continue
        fi

        # 检查端口可访问性
        local exposed_port=$(docker port "$container_id" | grep -o -m 1 '[0-9]\{4\}' | head -1)

        if [ -z "$exposed_port" ]; then
            log_warning "无法检测服务 $service 的暴露端口，跳过端口检查"
            log_success "服务 $service 容器状态正常"
            continue
        fi

        log "检查端口可访问性: $exposed_port"

        # 重试机制
        local retry_count=0
        local max_retries=5
        local retry_delay=4
        local health_check_passed=false

        while [[ $retry_count -lt $max_retries ]]; do
            if curl --fail --silent --show-error --max-time 5 "http://localhost:$exposed_port" > /dev/null 2>&1; then
                log_success "健康检查通过：服务 $service 在 http://localhost:$exposed_port 可访问"
                health_check_passed=true
                break
            else
                retry_count=$((retry_count + 1))
                if [[ $retry_count -lt $max_retries ]]; then
                    log_warning "健康检查失败：服务 $service 在端口 $exposed_port 不可访问。将在 ${retry_delay} 秒后重试... ($retry_count/$max_retries)"
                    sleep $retry_delay
                else
                    log_error "服务 $service 在端口 $exposed_port 的健康检查在 $max_retries 次重试后失败"
                    log_error "请检查容器日志: docker-compose logs --tail=100 $service"
                fi
            fi
        done

        if [ "$health_check_passed" = true ]; then
            log_success "服务 $service 部署成功！"
        else
            log_error "服务 $service 健康检查失败！"
        fi

        echo "----------------------------------------"
    done

    log_success "健康检查完成"
}

# 清理未使用的镜像
cleanup() {
    log "清理未使用的镜像..."
    docker image prune -f
    log_success "清理完成"
}

# 主函数
main() {
    log "===== 开始 MOER_OVERSEAS 服务器部署 ====="

    # 检查依赖
    check_dependencies

    # 拉取最新镜像
    pull_images

    # 部署服务
    deploy_services "$@"

    # 健康检查
    if [ $# -eq 0 ]; then
        # 如果没有指定服务，检查默认服务
        health_check "moer_overseas"
    else
        # 检查指定的服务
        health_check "$@"
    fi

    # 清理
    cleanup

    log_success "===== MOER_OVERSEAS 部署完成 ====="
}

# 执行主函数，传递所有参数
main "$@"
