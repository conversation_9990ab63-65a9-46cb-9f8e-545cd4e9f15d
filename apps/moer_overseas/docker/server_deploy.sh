#!/bin/bash

# MOER_OVERSEAS 服务器部署脚本
# 用法: ./server_deploy.sh [service1] [service2] ...
# 如果没有指定服务，则部署所有服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# 检查 Docker 和 Docker Compose 是否安装
check_dependencies() {
    log "检查依赖..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi

    log_success "依赖检查通过"
}

# 拉取最新镜像
pull_images() {
    log "拉取最新镜像..."
    docker-compose pull || docker compose pull
    log_success "镜像拉取完成"
}

# 部署指定服务
deploy_services() {
    local services=("$@")

    if [ ${#services[@]} -eq 0 ]; then
        log "未指定服务，部署所有服务..."
        docker-compose up -d || docker compose up -d
    else
        log "部署指定服务: ${services[*]}"
        docker-compose up -d "${services[@]}" || docker compose up -d "${services[@]}"
    fi

    log_success "服务部署完成"
}

# 清理未使用的镜像
cleanup() {
    log "清理未使用的镜像..."
    docker image prune -f
    log_success "清理完成"
}

# 主函数
main() {
    log "===== 开始 MOER_OVERSEAS 服务器部署 ====="

    # 检查依赖
    check_dependencies

    # 拉取最新镜像
    pull_images

    # 部署服务
    deploy_services "$@"

    # 清理
    cleanup

    log_success "===== MOER_OVERSEAS 部署完成 ====="
}

# 执行主函数，传递所有参数
main "$@"
