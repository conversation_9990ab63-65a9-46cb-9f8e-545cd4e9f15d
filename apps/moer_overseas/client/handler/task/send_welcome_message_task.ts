import { Node } from '../../../workflow/nodes/types'
import { sleep } from 'lib/schedule/schedule'
import { IChattingFlag } from '../../../state/user_flags'
import { IMoerUserInfo, MoerOverseasAPI } from 'model/moer_overseas_api/moer_overseas_api'
import { HumanTransfer, HumanTransferType } from '../../../human_transfer/human_transfer'
import { getUserId } from 'config/chat_id'
import logger from 'model/logger/logger'
import {
  chatDBClient,
  chatDBCommonClient,
  chatStateStoreClient,
  yCloudCommonMessageSender,
  yCloudMessageSender
} from '../../../service/instance'
import { UserLanguage } from '../../../helper/language/user_language_verify'
import { DataService } from '../../../helper/getter/get_data'
import { startTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { calSopTime } from '../../../visualized_sop/visualized_sop_processor'
import { PrismaMongoClient } from '../../../helper/mongodb/prisma'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../../../schedule/type'

export class SendWelcomeMessageTask {


  public static async sendWelcomeMessage(chatId: string, phoneNumber: string, name:string) {

    try {
      await this.newCourseUser(chatId, phoneNumber, name)
      await yCloudMessageSender.sendById({
        chat_id: chatId,
        ai_msg: await this.getWelcomeMessage(chatId),
        type: 'text',
      })
    } catch (e) {
      logger.warn(`客户手机号绑定失败，phoneNumber:${phoneNumber}`, e)
      await this.sendCourseRegisterMessage(chatId, phoneNumber, name)
    }

  }


  public static async newCourseUser(chatId: string, phoneNumber: string, name:string) {

    phoneNumber = phoneNumber.replace(/[^0-9]/g, '')

    await chatStateStoreClient.update(chatId, {
      nextStage: Node.FreeTalk
    })


    await chatDBClient.updatePhoneNumber(chatId, phoneNumber)
    await chatDBCommonClient.updateContact(chatId, getUserId(chatId), name)
    const moerData = await MoerOverseasAPI.getUserByPhone(phoneNumber)
    const email = moerData.email
    const moerId = moerData.id.toString()
    await chatDBClient.updateMoerId(chatId, moerId)
    await chatDBClient.updateEmail(chatId, email)
    await this.updateLanguage(chatId, moerId)
    await this.updateCourseNo(chatId, moerData)
    await startTasks(chatStateStoreClient, 'moer_overseas', getUserId(chatId), chatId, calSopTime)

    //绑定时区信息
    // await chatStateStoreClient.update(chatId, {
    //   state: <IChattingFlag>{
    //     time_zone:'UTC+8:00'
    //   }
    // })
  }

  private static async updateLanguage(chatId: string, moerId: string) {
    const rumenyingOrder = await PrismaMongoClient.getInstance().runmenying_order.findFirst({
      where:{
        moer_id: moerId
      }
    })
    if (rumenyingOrder?.language === 'chinese') {
      await chatStateStoreClient.update(chatId, { state:<IChattingFlag> { language: UserLanguage.Language_ZH } })
    } else {
      await chatStateStoreClient.update(chatId, { state:<IChattingFlag> { language: UserLanguage.Language_EN } })
    }
  }

  private static async updateCourseNo(chatId: string, moerData: IMoerUserInfo) {
    const courseNo = DataService.parseCourseNo(moerData)

    await chatDBClient.updateCourseNo(chatId, courseNo)
  }

  private static async sendCourseRegisterMessage(chatId: string, phoneNumber: string, name: string) {
    // 缺个领课链接
    const language = await UserLanguage.getLanguage(chatId)
    let msg = ''
    if (language === UserLanguage.Language_EN)
    {
      msg = `Hi, welcome to Teacher Ning's 5-Day Meditation Practice program. I’m your meditation coach, and I’ll be with you one-on-one throughout your learning journey. Feel free to reach out anytime if you have any questions—I’m happy to help!

Only 8 free spots left for this session—secure your spot now by clicking the link below:
xxxxxxxxx

After signing up, please reply with [Registered].`
    } else {
      msg = `Hi，歡迎參加唐寧老師的【5天冥想練習】，我是你的專屬冥想教練，之後會1對1參與到你的冥想學習之旅，有任何問題歡迎和我隨時交流探討哦~

本期免費名額只剩8個了，點擊下方連結報名哦
xxxxxxxxx

報名後回覆【已報名】`
    }
    await yCloudCommonMessageSender.sendText(chatId, {
      type: SendMessageType.text,
      text: msg,
      description:'[发送入门营领课链接]'
    })

    await SilentReAsk.schedule(TaskName.bindPhone, chatId, 30 * 60 * 1000, { phoneNumber:phoneNumber, name:name }, { auto_retry: true })
  }

  private static async startIntentionQuery1(chatId: string, userId: string) {
    const isSendIntentionQuery = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_send_user_query1

    if (!isSendIntentionQuery) {
      await yCloudMessageSender.sendById({
        chat_id: chatId,
        ai_msg: '唐宁老师希望收集大家的需求以调整课程内容',
        type: 'text'
      })

      await sleep(2000)

      await yCloudMessageSender.sendById({
        chat_id: chatId,
        ai_msg: `🙆🏻‍♀️您目前的生活角色？
1-职场奋斗者
2-家庭管理者
3-退休精进者
4-修行者

🐣您的冥想经验值？
5-纯小白
6-接触过
7-有基础

🎯最想点亮的人生议题
8-情绪减压
9-专注提升
10-睡眠改善
11-财富能量
12-亲密关系
13-灵性成长

——————
回复班班数字就好，例如：1-5-8，班班统计好，课前给老师参考`,
        type: 'text'
      })

      await chatStateStoreClient.update(chatId, {
        state: <IChattingFlag>{
          is_send_user_query1: true } })

      // 15min不回复
      // await SilentReAsk.schedule(chatId, async () => {
      //   await yCloudMessageSender.sendById({
      //     user_id: userId,
      //     chat_id: chatId,
      //     ai_msg: '在忙吗？方便的时候您回复下老师上面的问卷哈，帮助唐宁老师更了解你们哈',
      //   })
      // }, 15 * 60 * 1000)

    }
  }




  private static async getWelcomeMessage(chatId: string) {
    const language = await UserLanguage.getLanguage(chatId)
    if (language === UserLanguage.Language_EN) {
      const courseLink = await DataService.getCourseLink(0, chatId)
      return `Hi there 🧘 Welcome to Teacher Tangning's 【5-Day Meditation Practice】！I’m your teaching assistant and will be with you one-on-one throughout this meditation journey. Feel free to reach out anytime if you have questions~

Our 5-day meditation bootcamp kicks off next Monday at 8 PM (Hong Kong Time)⏰
Expect 4 consecutive nights of live sessions—let's dive deep！

👉 Monday (Session 1): Emotional Stress Relief
👉 Tuesday (Session 2): Wealth Awakening
👉 Wednesday (Session 3): Enhancing Effectiveness
👉 Thursday (Session 4): Manifest Success

📚 Pre-class Preparation:
Mini Lesson: "Ocean Wave Stress Relief"
${courseLink}`
    } else if (language === UserLanguage.Language_ZH) {
      const courseLink = await DataService.getCourseLink(0, chatId)
      const courseStartTime = await DataService.getCourseStartTime(chatId)
      const startTimeStr = `${courseStartTime.getMonth()}.${courseStartTime.getDay()}`
      return `Hi，你好呀🧘歡迎參加唐寧老師的【5天冥想練習】，我是你的專屬助教老師，之後會1對1參與到你的冥想學習之旅，有任何問題歡迎和我隨時交流探討哦~

我們本次五天冥想訓練營會在下周一晚上⏰8點開始（香港時間），連續4個晚上都有直播課哈~

👉週一第1節《情緒解壓》
👉週二第2節《財富果園》
👉週三第3節《效能提升》
👉週四第4節《藍鷹預演》

📚課前必做準備：
學習小講堂（海浪減壓）
${courseLink}`
    }

    return ''
  }

}