import { Config } from 'config'
import { JuziAPI } from 'model/juzi/api'
import logger from 'model/logger/logger'
import { DateHelper } from 'lib/date/date'
import { ObjectUtil } from 'lib/object'
import { IChattingFlag } from '../../state/user_flags'
import { IMoerUserInfo, MoerOverseasAPI } from 'model/moer_overseas_api/moer_overseas_api'
import { isScheduleTimeAfter, taskTimeToCourseTime } from '../tool/creat_schedule_task'
import { PolyvAPI } from 'model/polyv/polyv'
import { StringHelper } from 'lib/string'
import dayjs from 'dayjs'
import { RegexHelper } from 'lib/regex/regex'
import { catchError } from 'lib/error/catchError'
import { getChatId } from 'config/chat_id'
import { IDanmu, DanmuDB } from '../../database/danmu_db'
import { PrismaMongoClient } from '../mongodb/prisma'
import { chatDBClient, chatStateStoreClient } from '../../service/instance'
import isoWeek from 'dayjs/plugin/isoWeek'
import { UserLanguage } from '../language/user_language_verify'
import { ChatStateStore } from 'service/local_cache/chat_state_store'

dayjs.extend(isoWeek)

export interface IScheduleTime {
  is_course_week?: boolean // true 表示课程周，false 表示课程前一周
  post_course_week?: number // 课程结束后的第几周，1 表示第一周，2 表示第二周，依此类推
  day: number // 1-7 表示周一到周日
  time: string // 格式为 'HH:MM:SS'，例如 '08:00:00'
}

export enum WecomTag {
  AIOn = 'AI on',
  AIOff = 'AI off',
  JoinedGroup = '进群',
}

interface CourseInfo {
  day: number
  is_recording?: boolean
}

interface ParsedResource {
  userId: string
  sku: string
  vodId?: number
  liveId?: number
  day: number
}

/**
 * 对 JuziAPI, MoerOverseasAPI, Config 等的 二次封装，方便获取信息
 */
export class DataService {
  public static async getWechatName(userId: string) {
    try {
      const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
      if (!currentSender) {
        logger.warn('获取客户信息失败', userId)
        return ''
      }

      return currentSender.name ?? ''
    } catch (e) {
      return ''
    }
  }

  public static getCounsellorName() {
    return Config.setting.BOT_NAME
  }

  /**
   * 获取客户当期课的开课时间
   * @param chatId
   */
  public static async getCourseStartTime(chatId: string) {
    try {
      // 获取客户属于哪一期
      const chat = await chatDBClient.getById(chatId)
      if (!chat) {
        throw new Error('无法获取客户信息')
      }
      if (!chat.course_no) {
        throw new Error('无法获取客户课程期数')
      }

      // 获取这一期的开课时间
      const courseInfo =  await this.getCourseInfoByCourseNo(chat.course_no)
      return DateHelper.parseDate(courseInfo.startTime)
    } catch (e) {
      return await DataService.getCourseStartTimeByCourseNo(DataService.getNextWeekCourseNo())
    }
  }

  public static async getAccountIdByMoerId(moerId: string) {
    const chat =  await chatDBClient.getByMoerId(moerId)

    if (!chat) {
      return null
    }

    return chat.wx_id
  }

  public static async isInClass (chat_id: string, course: CourseInfo) {
    if (course.day < 0 || course.day > 4) {
      throw new Error (' 课程天数不正确 ')
    }

    if (course.day === 0) { // 小课堂只有录播课
      course.is_recording = true
    }

    const dayStatusMap:Record<number, string> = {
      1: 'is_in_day1_class',
      2: 'is_in_day2_class',
      3: 'is_in_day3_class',
      4: 'is_in_day4_class'
    }

    // 获取客户状态
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)
    const status = dayStatusMap [course.day]

    // 如果状态已经显示到课，直接返回 true
    if (state?. [status]) {
      return true
    }

    // 获取 moerId
    const moerId = await DataService.getMoerIdByChatId (chat_id)

    if (!moerId) {
      throw new Error ('无法获取客户 moerId')
    }

    // 获取课程信息
    const courseMap = await this.getMoerCourses (moerId)
    let courseStatus

    if (courseMap [course.day] && courseMap [course.day].live && !course.is_recording) { // 直播课到课查询
      courseStatus = await this.getMoerCourseStatus (courseMap [course.day].live as ParsedResource)

      const currentTime = await this.getCurrentTime(chat_id)
      const isCurrentDay = currentTime.day === course.day

      // 如果是当天晚上，Polyv 直播回传到 Moer会很慢，直接去 Polyv 查询
      if (isCurrentDay) {
        const liveId = (courseMap[course.day].live as ParsedResource).liveId as number
        const courseInfo = await this.getMoerCourseStatus(courseMap[course.day].live as ParsedResource)
        courseStatus = await this.pullMoerCourseStatusFromPolyv(moerId, liveId, new Date(), courseInfo.duration)
        // courseStatus = {
        //   duration: courseInfo.duration,
        //   playbackTime: await MoerOverseasAPI.getUserCoursePlaybackTime(Number(moerId), liveId)
        // }
        // if (course.day != 0) {
        //   const userSlots = allState.userSlots
        //   if (course.day == 1 && state.is_in_day1_class_live_stream && userSlots.last_enter_live_time_day1) {
        //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day1 / 1000))
        //   } else if (course.day == 2 && state.is_in_day2_class_live_stream && userSlots.last_enter_live_time_day2) {
        //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day2 / 1000))
        //   } else if (course.day == 3 && state.is_in_day3_class_live_stream && userSlots.last_enter_live_time_day3) {
        //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day3 / 1000))
        //   } else if (course.day == 4 && state.is_in_day4_class_live_stream && userSlots.last_enter_live_time_day4) {
        //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day4 / 1000))
        //   }
        // }
      } else {
        courseStatus = await this.getMoerCourseStatus(courseMap[course.day].live as ParsedResource)
      }
    } else if (courseMap[course.day] && course.is_recording && courseMap[course.day].record) { // 录播课到课查询
      courseStatus = await this.getMoerCourseStatus(courseMap[course.day].record as ParsedResource)
    } else {
      return false
    }

    if (!courseStatus || ObjectUtil.isEmptyObject(courseStatus)) {
      return false
    }

    let playBackTime = courseStatus.playbackTime

    if (typeof playBackTime === 'string') {
      playBackTime = Number (playBackTime)
    }

    // 判断是否到课（播放时间超过 5 分钟）
    let isInClass = playBackTime >= 5 * 60 // 5 分钟 = 300 秒

    // 小课堂 看了 3min 以上就算到课
    if (course.day === 0) {
      isInClass = playBackTime >= 3 * 60
    }

    // 如果距离开课时间 <= 30 分钟，看过 x 分钟就算到课
    if (new Date().getHours() === 20 && new Date().getMinutes() <= 30) {
      const courseStartTime = new Date().setHours(20, 0, 0, 0)
      const diffMillis =  Date.now() - courseStartTime
      const remainingMinutes = Math.floor(diffMillis / 60000)
      const thresholdMinutes = Math.floor(remainingMinutes / 10)

      isInClass = playBackTime > (thresholdMinutes === 0 ?  2 * 60 : 2 * thresholdMinutes * 60) // playbackTime 必须大于 0, 不能用 >=
    }
    logger.log(`chat_id:${chat_id} 上课时间 day${course.day} 是否录播:${course.is_recording ?? false} 持续时间:${playBackTime}`)

    if (isInClass) {
      // 更新状态
      await chatStateStoreClient.update(chat_id, { state:<IChattingFlag>{ ...state, [status]:true } })
    }

    return isInClass
  }


  /**
   * 主动获取课程完成情况，课程可以传 0，1，2，3，4，is_recording
   */
  public static async isCompletedCourse(chat_id: string, course: CourseInfo) {
    if (course.day < 0 || course.day > 4) {
      throw new Error('课程天数不正确')
    }

    const dayStatusMap:any = {
      0: 'is_complete_pre_course',
      1: {
        course: 'is_complete_day1_course',
        recording: 'is_complete_day1_course_recording'
      },
      2: {
        course: 'is_complete_day2_course',
        recording: 'is_complete_day2_course_recording'
      },
      3: {
        course: 'is_complete_day3_course',
        recording: 'is_complete_day3_course_recording'
      },
      4: {
        course: 'is_complete_day4_course',
        recording: 'is_complete_day4_course_recording'
      }
    }

    // 获取 客户状态
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)
    const status = dayStatusMap[course.day]

    if (course.day === 0 && state[status]) {
      return true
    } else if (status && course.is_recording && state[status.recording]) {
      return true
    } else if (status && state[status.course]) {
      return true
    }

    // 获取 moerId
    const moerId = await DataService.getMoerIdByChatId(chat_id)

    if (!moerId) {
      return false
    }

    // 获取课程完成情况
    const courseMap = await this.getMoerCourses(moerId)
    let courseStatus

    if (course.day === 0 || course.is_recording) {
      if (courseMap[course.day] && courseMap[course.day].record) {
        courseStatus = await this.getMoerCourseStatus(courseMap[course.day].record as ParsedResource)
      }
    } else {
      if (courseMap[course.day] && courseMap[course.day].live) {
        const currentTime = await this.getCurrentTime(chat_id)
        const isCurrentDay = currentTime.day === course.day

        // 如果是当天晚上，Polyv 直播回传到 Moer会很慢，直接去 Polyv 查询
        if (isCurrentDay) {
          const liveId = (courseMap[course.day].live as ParsedResource).liveId as number
          const courseInfo =  await this.getMoerCourseStatus(courseMap[course.day].live as ParsedResource)
          courseStatus = await this.pullMoerCourseStatusFromPolyv(moerId, liveId, new Date(), courseInfo.duration)
          // courseStatus = {
          //   duration: courseInfo.duration ? courseInfo.duration : (currentTime.is_course_week ? 5400 : 10 * 60),
          //   playbackTime: await MoerOverseasAPI.getUserCoursePlaybackTime(Number(moerId), liveId)
          // }

          // if (course.day != 0) {
          //   const userSlots = allState.userSlots
          //   if (course.day == 1 && state.is_in_day1_class_live_stream && userSlots.last_enter_live_time_day1) {
          //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day1 / 1000))
          //   } else if (course.day == 2 && state.is_in_day2_class_live_stream && userSlots.last_enter_live_time_day2) {
          //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day2 / 1000))
          //   } else if (course.day == 3 && state.is_in_day3_class_live_stream && userSlots.last_enter_live_time_day3) {
          //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day3 / 1000))
          //   } else if (course.day == 4 && state.is_in_day4_class_live_stream && userSlots.last_enter_live_time_day4) {
          //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day4 / 1000))
          //   }
          // }
        } else {
          courseStatus = await this.getMoerCourseStatus(courseMap[course.day].live as ParsedResource)
        }
      }
    }

    if (!courseStatus || ObjectUtil.isEmptyObject(courseStatus)) {
      return false
    }

    const courseDuration = courseStatus.duration > 0 ? courseStatus.duration : 5400
    let playBackTime = courseStatus.playbackTime

    if (typeof playBackTime === 'string') {
      playBackTime = Number(playBackTime)
    }
    logger.log(`chat_id:${chat_id} 上课时间 day${course.day} 是否录播:${course.is_recording ?? false} 持续时间:${playBackTime}`)

    // 小课堂按照 30% 算完课，其他按 60% 算完课
    const threshHold = course.day === 0 ? 0.3 : 0.6

    const isComplete = playBackTime / courseDuration >= threshHold

    if (isComplete) {
      // 更新状态
      if (course.day === 0) {
        await chatStateStoreClient.update(chat_id, { state:<IChattingFlag>{ ...state, is_complete_pre_course:true } })
      } else {
        await chatStateStoreClient.update(chat_id, { state:<IChattingFlag>{ ...state, [course.is_recording ? status.recording : status.course]: true } })
      }
    }

    return isComplete
  }

  public static async isAttendCourseMoreThanOneSecond(chat_id: string, course: CourseInfo) {
    if (course.day < 0 || course.day > 4) {
      throw new Error('课程天数不正确')
    }

    const dayStatusMap:any = {
      0: 'is_attend_pre_course_one_second',
      1: {
        course: 'is_attend_day1_course_one_second',
        recording: 'is_attend_day1_course_recording_one_second'
      },
      2: {
        course: 'is_attend_day2_course_one_second',
        recording: 'is_attend_day2_course_recording_one_second'
      },
      3: {
        course: 'is_attend_day3_course_one_second',
        recording: 'is_attend_day3_course_recording_one_second'
      },
      4: {
        course: 'is_attend_day4_course_one_second',
        recording: 'is_attend_day4_course_recording_one_second'
      }
    }

    // 获取 客户状态
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)
    const status = dayStatusMap[course.day]

    if (course.day === 0 && state[status]) {
      return true
    } else if (status && course.is_recording && state[status.recording]) {
      return true
    } else if (status && state[status.course]) {
      return true
    }

    // 获取 moerId
    const moerId = await DataService.getMoerIdByChatId(chat_id)

    if (!moerId) {
      return false
    }

    // 获取课程完成情况
    const courseMap = await this.getMoerCourses(moerId)
    let courseStatus

    if (course.day === 0 || course.is_recording) {
      if (courseMap[course.day] && courseMap[course.day].record) {
        courseStatus = await this.getMoerCourseStatus(courseMap[course.day].record as ParsedResource)
      }
    } else {
      if (courseMap[course.day] && courseMap[course.day].live) {
        const currentTime = await this.getCurrentTime(chat_id)
        const isCurrentDay = currentTime.day === course.day

        // 如果是当天晚上，Polyv 直播回传到 Moer会很慢，直接去 Polyv 查询
        if (isCurrentDay) {
          const liveId = (courseMap[course.day].live as ParsedResource).liveId as number
          const courseInfo =  await this.getMoerCourseStatus(courseMap[course.day].live as ParsedResource)
          courseStatus = await this.pullMoerCourseStatusFromPolyv(moerId, liveId, new Date(), courseInfo.duration)
          // courseStatus = {
          //   duration: courseInfo.duration,
          //   playbackTime: await MoerOverseasAPI.getUserCoursePlaybackTime(Number(moerId), liveId)
          // }
          // if (course.day != 0) {
          //   const userSlots = allState.userSlots
          //   if (course.day == 1 && state.is_in_day1_class_live_stream && userSlots.last_enter_live_time_day1) {
          //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day1 / 1000))
          //   } else if (course.day == 2 && state.is_in_day2_class_live_stream && userSlots.last_enter_live_time_day2) {
          //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day2 / 1000))
          //   } else if (course.day == 3 && state.is_in_day3_class_live_stream && userSlots.last_enter_live_time_day3) {
          //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day3 / 1000))
          //   } else if (course.day == 4 && state.is_in_day4_class_live_stream && userSlots.last_enter_live_time_day4) {
          //     courseStatus.playbackTime += Math.floor((Date.now() / 1000) - (userSlots.last_enter_live_time_day4 / 1000))
          //   }
          // }
        } else {
          courseStatus = await this.getMoerCourseStatus(courseMap[course.day].live as ParsedResource)
        }
      }
    }

    if (!courseStatus || ObjectUtil.isEmptyObject(courseStatus)) {
      return false
    }

    let playBackTime = courseStatus.playbackTime

    if (typeof playBackTime === 'string') {
      playBackTime = Number(playBackTime)
    }
    logger.log(`chat_id:${chat_id} 上课时间 day${course.day} 是否录播:${course.is_recording ?? false} 持续时间:${playBackTime}`)

    // 小课堂按照 30% 算完课，其他按 60% 算完课

    const isAttendOneSecond = playBackTime >= 1

    if (isAttendOneSecond) {
      // 更新状态
      if (course.day === 0) {
        await chatStateStoreClient.update(chat_id, { state:<IChattingFlag>{ ...state, [status]:true } })
      } else {
        await chatStateStoreClient.update(chat_id, { state:<IChattingFlag>{ ...state, [course.is_recording ? status.recording : status.course]:true } })
      }
    }

    return isAttendOneSecond
  }

  public static async isInGroup(user_id: string) {
    return await JuziAPI.isInGroup({
      imBotId: Config.setting.wechatConfig?.id as string,
      imRoomId: Config.setting.wechatConfig?.classGroupId as string,
      imContactId: user_id
    })
  }

  /**
   * 是否完成所有课程(含录播课完课)
   */
  public static async isCompletedAllCourse(chat_id: string) {
    const liveStreamCompleteTask: Promise<boolean>[] = []
    const recordingCompleteTask: Promise<boolean>[] = []
    for (let i = 1; i <= 4; i++) {
      liveStreamCompleteTask.push(this.isCompletedCourse(chat_id, { day: i }))
      recordingCompleteTask.push(this.isCompletedCourse(chat_id, { day: i, is_recording: true }))
    }

    const liveStreamComplete = await Promise.all(liveStreamCompleteTask)
    const recordingComplete = await Promise.all(recordingCompleteTask)
    for (let i = 0; i < 4; i++) {
      if (!liveStreamComplete[i] && !recordingComplete[i]) {
        return false
      }
    }
    return true
  }

  /**
   * 获取课程完成情况
   */
  public static async getMoerCourseStatus(course: ParsedResource) {
    return await MoerOverseasAPI.getUserChapterStatus({
      userId: course.userId,
      liveId: course.liveId?.toString(),
      vodId: course.vodId?.toString(),
      sku: course.sku
    })
  }


  /**
   * 获取课程信息
   * @param moerId
   */
  public static async getMoerCourses(moerId: string) {
    const resourceMap : {[key: number]: {
        live?: ParsedResource
        record?: ParsedResource
      }} = {}

    // 获取课程列表
    const courses = await MoerOverseasAPI.getUserCourses(moerId)

    courses.forEach((course) => {
      const sku = course.sku
      course.resource.forEach((res) => {
        const day = res.day === 5 ? 4 : res.day // 修正 day 为 4

        // 初始化天数的资源对象，如果还未存在
        if (!resourceMap[day]) {
          resourceMap[day] = {}
        }

        if (res.vodId) {
          resourceMap[day].record = {
            userId: moerId,
            sku: sku,
            vodId: res.vodId,
            day: day
          }
        }

        if (res.liveId) {
          resourceMap[day].live = {
            userId: moerId,
            sku: sku,
            liveId: res.liveId,
            day: day
          }
        }
      })
    })

    return resourceMap
  }

  public static async getMoerIdByChatId(chatId: string) {
    const chat =  await chatDBClient.getById(chatId)

    if (!chat) {
      return null
    }

    return chat.moer_id
  }

  public static async getChatIdByMoerId(moerId: string) {
    const chat = await chatDBClient.getByMoerId(moerId)

    if (!chat) {
      return null
    }

    return chat.id
  }

  public static async getEnergyTestScore(chatId: string): Promise<number | null> {
    let score
    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).energy_test_score || (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).energy_test_score === 0) { // 注意，有可能是 0 分
      score = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).energy_test_score
    } else {
      const moerId = await DataService.getMoerIdByChatId(chatId)
      if (!moerId) {
        return null
      }

      const scoreList = await MoerOverseasAPI.getUserEnergyMark(moerId)
      if (scoreList.data.list && scoreList.data.list.length > 0) {
        score = scoreList.data.list[scoreList.data.list.length - 1].examScore

        await chatStateStoreClient.update(chatId, {
          state: <IChattingFlag>{
            energy_test_score: score
          }
        })
      } else {
        return null
      }
    }

    return score ?? null
  }

  public static async getCurrentTime(chatId: string) {
    try {
      return await taskTimeToCourseTime(new Date(), chatId)
    } catch (e) {
      // 当客户没有绑定课程时，返回当前时间
      return {
        is_course_week: false,
        day: new Date().getDay() === 0 ? 7 : new Date().getDay(),
        time: DateHelper.formatDate(new Date(), 'HH:mm:ss')
      }
    }
  }

  public static async getTimeDiffByUserTimeZone(chatId: string) {
    const timeZoneMap = {
      'UTC+8:00': 0,
      'UTC+9:00': 1,
      'UTC+10:00': 2,
    }

    const userTimeZone = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).time_zone

    if (!userTimeZone) {
      return 0
    }

    return timeZoneMap[userTimeZone]
  }

  public static async getDanmuByChatId(chatId: string) {
    const moerId = await DataService.getMoerIdByChatId(chatId)
    if (!moerId) {
      return []
    }

    return DanmuDB.getDanmusByMoerId(moerId)
  }

  /**
   * 获取客户在线状态
   */
  static parseCourseNo(moerUser: IMoerUserInfo) {
    let maxStage = -Infinity // Initialize with a very low number to track the max stage found

    for (const item of moerUser.userGoodsSeries) {
      if (item.type === 1 && item.stage > maxStage) {
        maxStage = item.stage
      }
    }

    // If maxStage is still -Infinity, no items with type === 1 were found
    if (maxStage !== -Infinity) {
      return maxStage
    }

    return DataService.getNextWeekCourseNo()
  }

  static async getCourseLinkByCourseNo(course_no: number, day: number, is_recording?: boolean) {
    const courseInfo = await MoerOverseasAPI.getCurrentCourseInfo(course_no)
    if (!courseInfo) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }

    if (courseInfo.code !== 0) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }

    const course = courseInfo.data.resource.find((item) => item.day === day)
    if (!course) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }

    if (is_recording) {
      return course.vodShortUrl
    }

    return course.liveShortUrl
  }


  static async getPreCourseLinkByCourseNo(course_no: number) {
    const courseInfo = await MoerOverseasAPI.getCurrentCourseInfo(course_no)
    if (!courseInfo) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }

    const course = courseInfo.data.resource.find((item) => item.day === 0)
    if (!course) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }


    return course.liveShortUrl
  }

  static async getCourseLink(day: number, chat_id: string, is_recording?: boolean) {
    const chat = await chatDBClient.getById(chat_id)
    let courseNo = chat?.course_no
    if (!courseNo) {
      courseNo = DataService.getCurrentWeekCourseNo()
    }

    const courseInfo = await MoerOverseasAPI.getCurrentCourseInfo(courseNo, await this.getCourseSid(chat_id))
    if (!courseInfo) {
      logger.warn('获取课程信息失败1', chat_id, courseNo)
      return ''
    }

    if (courseInfo.code !== 0) {
      logger.warn('获取课程信息失败2', chat_id, courseNo)
      return ''
    }

    const course = courseInfo.data.resource.find((item) => item.day === day)
    if (!course) {
      logger.warn('获取课程信息失败3', chat_id, courseNo, day)
      return ''
    }

    if (is_recording) {
      return course.vodShortUrl
    }

    return course.liveShortUrl
  }

  static async pullLiveDanmuAndStore (liveId: string, formattedDate: string, formattedDate2: string, courseNo: number, day: number) {
    const data = await PolyvAPI.getLiveDanmu(liveId, formattedDate, formattedDate2)
    // 过滤一下 弹幕
    const filteredData = data.filter((item) => StringHelper.isNumber(item.user.userId))

    const simplifiedData = filteredData.map((item) => {
      return {
        liveId: liveId,
        content: item.content,
        time: item.time,
        userId: item.user.userId,
        userName: item.user.nickname,
        courseNo: courseNo,
        sendTime: new Date(item.time),
        day: day
      }
    }).filter((item) => typeof item.content === 'string') // 文件类型先不处理

    // 存到数据库中
    await DanmuDB.saveDanmu(simplifiedData as IDanmu[])
  }

  public static async getCourseInfoByCourseNo(course_no: number) {
    const courseInfo = await MoerOverseasAPI.getCurrentCourseInfo(course_no)

    if (courseInfo.code !== 0) {
      throw new Error(`获取课程信息失败: ${courseInfo.msg}`)
    }

    return courseInfo.data
  }

  public static async getCourseStartTimeByCourseNo(course_no: number) {
    try {
      const courseInfo = await MoerOverseasAPI.getCurrentCourseInfo(course_no)

      if (courseInfo.code !== 0) {
        throw new Error(`获取课程信息失败: ${courseInfo.msg}`)
      }

      return DateHelper.parseDate(courseInfo.data.startTime)
    } catch (e) {
      //默认返回下周一
      const day = 8 - new Date().getDay()
      return DateHelper.add(new Date(), day, 'day')
    }
  }


  private static async pullMoerCourseStatusFromPolyv(userId: string, liveId: number, date: Date, duration: number) {

    const viewLogs = await PolyvAPI.getViewLog(userId, DateHelper.formatDate(date, 'YYYY-MM-DD'), DateHelper.formatDate(date, 'YYYY-MM-DD'))
    let playbackTime = 0
    if (viewLogs.code !== 200) {
      return {
        duration: duration,
        playbackTime: playbackTime
      }
    }
    for (const content of viewLogs.data.contents) {
      if (content.channelId === liveId) {
        playbackTime += this.timeStringToSeconds(content.playDuration)
      }
    }

    return {
      duration: duration,
      playbackTime: playbackTime
    }
  }

  private static timeStringToSeconds(playDuration: string) {
    const timeParts = playDuration.split(':')
    let seconds = 0

    if (timeParts.length === 3) {
      // 格式为 HH:MM:SS
      const hours = parseInt(timeParts[0], 10)
      const minutes = parseInt(timeParts[1], 10)
      const secs = parseInt(timeParts[2], 10)

      seconds = hours * 3600 + minutes * 60 + secs
    } else if (timeParts.length === 2) {
      // 格式为 MM:SS
      const minutes = parseInt(timeParts[0], 10)
      const secs = parseInt(timeParts[1], 10)

      seconds = minutes * 60 + secs
    } else if (timeParts.length === 1) {
      // 格式为 SS
      seconds = parseInt(timeParts[0], 10)
    } else {
      throw new Error('无效的时间格式')
    }

    return seconds
  }

  static async isPaidSystemCourse(chatId: string) {
    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_complete_payment) {
      return true
    }

    const moerId = await DataService.getMoerIdByChatId(chatId)
    if (!moerId) {
      return false
    }

    const courses =  await MoerOverseasAPI.getUserCourses(moerId)
    return courses.some((course) => course.type === 2)
  }

  static async isInAnyClass(chatId: string) {
    for (let i = 1; i <= 4; i++) {
      const isInLive = await this.isInClass(chatId, { day: i })
      if (isInLive) {
        return true
      }
      const isInRecord = await this.isInClass(chatId, { day: i, is_recording: false })
      if (isInRecord) {
        return true
      }
    }

    return false
  }

  // 根据相对时间获取期数
  public static getCurrentWeekCourseNo(): number {
    const date = new Date()
    const startDate = dayjs('2024-11-15')        // 开营日（第 0 期）
    const startOfStartWeek = startDate.startOf('isoWeek')
    const startOfCurrentWeek = dayjs(date).startOf('isoWeek')

    // 原本你就减了 1，没动它
    return startOfCurrentWeek.diff(startOfStartWeek, 'week') - 1
  }

  public static getNextWeekCourseNo(): number {

    return DataService.getCurrentWeekCourseNo() + 1
  }

  static async getWxIdByMoerId(moerId: string) {
    const chat =  await chatDBClient.getByMoerId(moerId)

    if (!chat) {
      return null
    }

    return chat.wx_id
  }

  /**
   * 粗筛，有可能有重名的客户，不要用在生产环境！！！！！
   * @param name
   */
  static async getChatByWechatName(name: string) {
    return PrismaMongoClient.getInstance().chat.findRaw({
      filter: {
        'contact.wx_name': name
      }
    })
  }

  static async getCourseNoByChatId(chatId: string) {
    const chat = await chatDBClient.getById(chatId)
    if (!chat) {
      return null
    }

    return chat.course_no
  }

  public static tagsMap = new Map<string, string>() // tagName => tagId

  public static async isWithinClassTime(currentTime: IScheduleTime) {
    // 定义每一天的上课结束时间，提前 10 分钟
    const dayEndTimeMap:Record<number, string> = {
      1: '21:35:00', // 周一
      2: '21:05:00', // 周二
      3: '21:40:00', // 周三
      4: '22:05:00', // 周四
    }

    // 检查是否为上课周
    if (!currentTime.is_course_week) {
      return false
    }

    const currentDay = currentTime.day

    // 检查是否在星期一到星期四
    if (currentDay < 1 || currentDay > 4) {
      return false
    }

    // 获取当前时间
    const currentTimeStr = currentTime.time

    // 定义上课开始时间和当天结束时间
    const classStartTime = '20:05:00'
    const classEndTime = dayEndTimeMap[currentDay]

    // 检查当前时间是否在上课开始时间之后或等于，并且在上课结束时间之前或等于
    const afterStart = DateHelper.isTimeAfter(currentTimeStr, classStartTime) || currentTimeStr === classStartTime
    const beforeEnd = DateHelper.isTimeBefore(currentTimeStr, classEndTime) || currentTimeStr === classEndTime

    return afterStart && beforeEnd
  }

  public static async getTodayCourse(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)
    if (!currentTime.is_course_week) {
      return ''
    }
    const dayEndTimeMap = {
      1: '21:35:00', // 星期一
      2: '21:05:00', // 星期二
      3: '21:40:00', // 星期三
      4: '22:05:00'  // 星期四
    }
    let todayCourse = ''
    let tomorrowCourse = ''
    if (currentTime.day > 0 && currentTime.day < 4) {
      const isAfterCourse = isScheduleTimeAfter(currentTime, { is_course_week: true, day: currentTime.day, time: dayEndTimeMap[currentTime.day] })
      const isInCourse = await DataService.isWithinClassTime(currentTime)
      const courseStatus = isAfterCourse ? '，已结束' : (isInCourse ? '，已开始' : '，未开始')
      todayCourse  = `今日课程：第${currentTime.day}课${courseStatus}。`
    }
    if (currentTime.day >= 0 && currentTime.day < 4) {
      tomorrowCourse = `明日课程：第${currentTime.day + 1}课`
    }
    if (currentTime.day === 4) {
      const isAfterCourse = isScheduleTimeAfter(currentTime, { is_course_week: true, day: currentTime.day, time: dayEndTimeMap[currentTime.day] })
      const isInCourse = await DataService.isWithinClassTime(currentTime)
      const courseStatus = isAfterCourse ? '，已结束' : (isInCourse ? '，已开始' : '，未开始')
      todayCourse  = `今日课程：第${currentTime.day}课${courseStatus}。`
    }

    return `${todayCourse}${tomorrowCourse}`
  }

  /**
   * 是否曾经买过入门营的课程，注意调用前要先检查客户是否购买过课程
   */
  public static async hasPurchasedIntroCourseBefore(moerId: string) {
    // 查询课程
    const userCourses = await MoerOverseasAPI.getUserCourses(moerId)
    let introCourseCount = 0

    for (const userCourse of userCourses) {
      if (userCourse.sid === 1) {
        if (userCourse.is_refund === 1) {
          continue
        }

        introCourseCount ++

        // if (userCourse.stage < DataService.getCurrentWeekCourseNo()) {
        //   return true
        // }
      }
    }

    return introCourseCount > 1
  }

  static async getSystemCourseStartTime(chatId: string) {
    // 获取 MoerId
    const moerId = await DataService.getMoerIdByChatId(chatId)
    if (!moerId) {
      return ''
    }

    // 查询课程
    const courses = await MoerOverseasAPI.getUserCourses(moerId)
    const systemCourse = courses.find((course) => course.type === 2)

    if (!systemCourse) {
      const systemCourseInfo = await MoerOverseasAPI.getSystemCourseInfo()
      return systemCourseInfo.start_time
    }

    return systemCourse.start_time
  }

  static async getChatsByCourseNo(courseNo: number) {
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: courseNo,
      }
    })

    return chats.filter((chat) => !Config.isInternalMember(chat.contact.wx_id) && !chat.id.includes('R') && !chat.id.includes('local'))
  }

  public static async findPhoneNumber(userId: string) {
    logger.trace(`findPhoneNumber userId: ${userId}`)

    const phoneNumber = (await chatStateStoreClient.getFlags<IChattingFlag>(getChatId(userId))).phoneNumber
    if (phoneNumber) {
      return phoneNumber
    }

    return ''
  }

  public static async getCourseSid(chatId: string) {
    const language = await UserLanguage.getLanguage(chatId)
    let sid = 1
    if (language === UserLanguage.Language_EN) {
      sid = 37
    } else if (language === UserLanguage.Language_ZH) {
      sid = 1
    }

    return sid
  }

  /**
   * 判断客户是否领取了入门营课程
   * @param chatId
   */
  public static async isRegisterCourse(chatId: string) {
    const isResister = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).has_register_course
    if (isResister !== undefined) {
      return  isResister
    }

    try {
      const userInfo = await MoerOverseasAPI.getUserByPhone(await DataService.findPhoneNumber(chatId))
    } catch (e) {
      await chatStateStoreClient.update(chatId, {
        state:<IChattingFlag>{
          has_register_course : false
        }
      })
      return false
    }

    await chatStateStoreClient.update(chatId, {
      state:<IChattingFlag>{
        has_register_course : true
      }
    })

    return true

  }
}